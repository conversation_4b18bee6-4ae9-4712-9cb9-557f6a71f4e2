server:
  port: 8088

spring:
  application:
    name: creafec-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true   # active le routage automatique via Eureka
          lower-case-service-id: true
      routes:
        - id: client-service
          uri: lb://client-service
          predicates:
            - Path=/clients/**   # toutes les requêtes /clients/** vont vers client-service
        - id: invoice-service
          uri: lb://invoice-service
          predicates:
            - Path=/invoices/**  # toutes les requêtes /invoices/** vont vers invoice-service

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/  # URL du Eureka Server
    register-with-eureka: true
    fetch-registry: true
