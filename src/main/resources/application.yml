server:
  port: 8082

spring:
  application:
    name: client-service   # NOM du service dans Eureka
  datasource:
    url: ************************************************
    username: postgres
    password: postgres
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
#  cloud:
#    config:
#      uri: http://localhost:8888   # Config Server
#      fail-fast: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/