package com.lunatem.creafac_client_service;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ClientService {

    private final ClientRepository clientRepository;

    public void createClient(ClientDTO clientDTO) {
        clientRepository.save(ClientDTO.toEntity(clientDTO));
    }

    public Set<ClientDTO> getAllClients() {
        return clientRepository.findAll().stream()
                .map(ClientDTO::fromEntity)
                .collect(Collectors.toSet());
    }

    public ClientDTO getClientById(Long id) {
        return clientRepository.findById(id).stream()
                .map(ClientDTO::fromEntity)
                .findFirst()
                .orElseThrow(() -> new EntityNotFoundException("Client not found"));
    }
}
