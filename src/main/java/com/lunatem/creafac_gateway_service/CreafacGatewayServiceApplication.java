package com.lunatem.creafac_gateway_service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication
@EnableDiscoveryClient
public class CreafacGatewayServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(CreafacGatewayServiceApplication.class, args);
	}

}
