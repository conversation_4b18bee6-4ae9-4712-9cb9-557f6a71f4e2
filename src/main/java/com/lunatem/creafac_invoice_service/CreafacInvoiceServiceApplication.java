package com.lunatem.creafac_invoice_service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.lunatem.creafac_invoice_service.client")
public class CreafacInvoiceServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(CreafacInvoiceServiceApplication.class, args);
	}

}
