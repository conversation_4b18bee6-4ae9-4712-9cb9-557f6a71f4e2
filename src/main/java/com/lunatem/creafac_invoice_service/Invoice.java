package com.lunatem.creafac_invoice_service;

import com.lunatem.creafac_invoice_service.client.ClientDTO;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Invoice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String invoiceNumber;
    private Long clientId;

    @Transient
    private ClientDTO client;
}
