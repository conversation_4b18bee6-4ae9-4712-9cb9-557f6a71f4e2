package com.lunatem.creafac_invoice_service;

import com.lunatem.creafac_invoice_service.client.ClientRestClient;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InvoiceService {

    private final InvoiceRepository invoiceRepository;
    private final ClientRestClient clientRestClient;

    public void createInvoice(InvoiceDTO invoiceDTO) {
        invoiceRepository.save(InvoiceDTO.toEntity(invoiceDTO));
    }

    public Set<InvoiceDTO> getAllInvoices() {
        return invoiceRepository.findAll().stream()
                .map(invoice -> {
                    InvoiceDTO dto = InvoiceDTO.fromEntity(invoice);
                    dto.setClient(clientRestClient.findClientById(invoice.getClientId())); // ajouter ton attribut ici
                    return dto;
                })
                .collect(Collectors.toSet());
    }

    public InvoiceDTO getInvoiceById(Long id) {
        return invoiceRepository.findById(id).stream()
                .map(InvoiceDTO::fromEntity)
                .findFirst()
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found"));
    }
}
