package com.lunatem.creafac_invoice_service;

import com.lunatem.creafac_invoice_service.client.ClientDTO;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceDTO {
    private String invoiceNumber;
    private Long clientId;
    private ClientDTO client;

    public static Invoice toEntity(InvoiceDTO invoiceDTO) {
        return Invoice.builder()
                .invoiceNumber(invoiceDTO.getInvoiceNumber())
                .clientId(invoiceDTO.getClientId())
                .client(invoiceDTO.getClient())
                .build();
    }

    public static InvoiceDTO fromEntity(Invoice invoice) {
        return InvoiceDTO.builder()
                .invoiceNumber(invoice.getInvoiceNumber())
                .clientId(invoice.getClientId())
                .client(invoice.getClient())
                .build();
    }
}
